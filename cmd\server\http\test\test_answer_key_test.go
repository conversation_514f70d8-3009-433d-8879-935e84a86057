package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestGetTestAnswerKey(t *testing.T) {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	// Test data names
	adminEmail := "answerkey_admin_" + timestamp + "@example.com"
	studentEmail := "answerkey_student_" + timestamp + "@example.com"
	subjectName := "Answer Key Physics " + timestamp
	testName := "Answer Key Test " + timestamp

	// Cleanup function
	defer func() {
		db.Exec("DELETE FROM test_responses WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM options WHERE question_id IN (SELECT q.id FROM questions q JOIN section_questions sq ON q.id = sq.question_id JOIN sections s ON sq.section_id = s.id JOIN tests t ON s.test_id = t.id WHERE t.name = ?)", testName)
		db.Exec("DELETE FROM section_questions WHERE section_id IN (SELECT id FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?))", testName)
		db.Exec("DELETE FROM questions WHERE topic_id IN (SELECT t.id FROM topics t JOIN chapters c ON t.chapter_id = c.id JOIN subjects s ON c.subject_id = s.id WHERE s.name = ?)", subjectName)
		db.Exec("DELETE FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM tests WHERE name = ?", testName)
		db.Exec("DELETE FROM test_type_section_types WHERE test_type_id IN (SELECT id FROM test_types WHERE name LIKE ?)", "%"+timestamp+"%")
		db.Exec("DELETE FROM test_types WHERE name LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM section_types WHERE name LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM difficulties WHERE name LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
		db.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email = ?)", studentEmail)
		db.Exec("DELETE FROM admins WHERE user_id IN (SELECT id FROM users WHERE email = ?)", adminEmail)
		db.Exec("DELETE FROM users WHERE email IN (?, ?)", adminEmail, studentEmail)
	}()

	// Create admin
	adminPayload := models.AdminForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Answer Key Admin",
			Email:          adminEmail,
			PhoneNumber:    "1111111111",
			ContactAddress: "Admin Street",
		},
		Password: "adminpassword123",
	}
	adminResp := requestExecutionHelper(http.MethodPost, "/api/admins", adminPayload)
	assert.Equal(t, http.StatusCreated, adminResp.Code)

	var adminResponse models.CreatedAdminResponse
	err := json.Unmarshal(adminResp.Body.Bytes(), &adminResponse)
	assert.Nil(t, err)
	adminToken := adminResponse.Token

	// Create student
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Answer Key Student",
			Email:          studentEmail,
			PhoneNumber:    "2222222222",
			ContactAddress: "Student Street",
		},
		ParentPhone: "3333333333",
		ParentEmail: "parent_" + timestamp + "@example.com",
		Password:    "studentpassword123",
	}
	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var studentResponse models.CreatedStudentResponse
	err = json.Unmarshal(studentResp.Body.Bytes(), &studentResponse)
	assert.Nil(t, err)
	studentToken := studentResponse.Token

	// Create test prerequisites
	subject := models.Subject{
		Name:        subjectName,
		DisplayName: subjectName,
	}
	db.Create(&subject)

	chapter := models.Chapter{
		Name:        "Test Chapter " + timestamp,
		DisplayName: "Test Chapter Display",
		SubjectID:   subject.ID,
	}
	db.Create(&chapter)

	topic := models.Topic{
		Name:      "Test Topic " + timestamp,
		ChapterID: chapter.ID,
	}
	db.Create(&topic)

	difficulty := models.Difficulty{
		Name: "Medium " + timestamp,
	}
	db.Create(&difficulty)

	// Create questions
	mcqQuestion := models.Question{
		Text:         "What is 2+2?",
		TopicID:      topic.ID,
		DifficultyID: difficulty.ID,
		QuestionType: "mcq",
	}
	db.Create(&mcqQuestion)

	// Create options for MCQ
	correctOption := models.Option{
		QuestionID: mcqQuestion.ID,
		OptionText: "4",
		IsCorrect:  true,
	}
	db.Create(&correctOption)

	wrongOption := models.Option{
		QuestionID: mcqQuestion.ID,
		OptionText: "5",
		IsCorrect:  false,
	}
	db.Create(&wrongOption)

	correctAnswer := "Paris"
	textQuestion := models.Question{
		Text:          "What is the capital of France?",
		TopicID:       topic.ID,
		DifficultyID:  difficulty.ID,
		QuestionType:  "text",
		CorrectAnswer: &correctAnswer,
	}
	db.Create(&textQuestion)

	// Create section type and test type
	sectionType := models.SectionType{
		Name:      "General " + timestamp,
		SubjectID: subject.ID,
	}
	db.Create(&sectionType)

	testType := models.TestType{
		Name: "General Test " + timestamp,
	}
	db.Create(&testType)
	db.Model(&testType).Association("SectionTypes").Append(&sectionType)

	// Create test
	test := models.Test{
		Name:        testName,
		TestTypeID:  testType.ID,
		FromTime:    time.Now(),
		ToTime:      time.Now().Add(2 * time.Hour),
		Active:      true,
		Description: "Test for answer key API",
	}
	db.Create(&test)

	// Create section and add questions
	section := models.Section{
		Name:          "General Section " + timestamp,
		DisplayName:   "General Section",
		TestID:        test.ID,
		SectionTypeID: sectionType.ID,
	}
	db.Create(&section)

	// Associate questions with section
	db.Model(&section).Association("Questions").Append(&mcqQuestion, &textQuestion)

	// Record student responses
	testResponses := models.TestResponsesForCreate{
		TestID: test.ID,
		Responses: []models.TestResponseForCreate{
			{
				QuestionID:        mcqQuestion.ID,
				SelectedOptionIDs: []int{int(correctOption.ID)}, // Correct answer
				ResponseText:      nil,
			},
			{
				QuestionID:        textQuestion.ID,
				SelectedOptionIDs: []int{},
				ResponseText:      func() *string { s := "London"; return &s }(), // Wrong answer
			},
		},
	}

	recordResp := authenticatedRequestHelper(http.MethodPost, "/api/test-responses", testResponses, studentToken)
	assert.Equal(t, http.StatusOK, recordResp.Code)

	// Test 1: Student gets answer key with their responses
	t.Run("StudentAnswerKey", func(t *testing.T) {
		url := fmt.Sprintf("/api/tests/%d/answer-key", test.ID)
		resp := authenticatedRequestHelper(http.MethodGet, url, nil, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var result models.TestAnswerKeyForStudent
		err := json.Unmarshal(resp.Body.Bytes(), &result)
		assert.Nil(t, err)

		// Verify basic test info
		assert.Equal(t, test.ID, result.TestID)
		assert.Equal(t, test.Name, result.TestName)
		assert.Equal(t, studentResponse.Student.ID, result.StudentID)
		assert.Equal(t, "Answer Key Student", result.StudentName)
		assert.Equal(t, 1, result.TotalScore) // 1 correct out of 2
		assert.Equal(t, 2, result.MaxPossibleScore)

		// Verify sections
		assert.Equal(t, 1, len(result.Sections))
		sectionResult := result.Sections[0]
		assert.Equal(t, 2, len(sectionResult.Questions))

		// Find MCQ question
		var mcqQuestionResult *models.TestAnswerKeyQuestionStudent
		for i := range sectionResult.Questions {
			if sectionResult.Questions[i].QuestionID == mcqQuestion.ID {
				mcqQuestionResult = &sectionResult.Questions[i]
				break
			}
		}
		assert.NotNil(t, mcqQuestionResult)
		assert.Equal(t, "What is 2+2?", mcqQuestionResult.Text)
		assert.Equal(t, "mcq", mcqQuestionResult.QuestionType)
		assert.NotNil(t, mcqQuestionResult.StudentResponse)
		assert.True(t, mcqQuestionResult.StudentResponse.IsCorrect)
		assert.True(t, mcqQuestionResult.StudentResponse.WasAttempted)
		assert.Equal(t, []int{int(correctOption.ID)}, mcqQuestionResult.StudentResponse.SelectedOptionIDs)

		// Find text question
		var textQuestionResult *models.TestAnswerKeyQuestionStudent
		for i := range sectionResult.Questions {
			if sectionResult.Questions[i].QuestionID == textQuestion.ID {
				textQuestionResult = &sectionResult.Questions[i]
				break
			}
		}
		assert.NotNil(t, textQuestionResult)
		assert.Equal(t, "What is the capital of France?", textQuestionResult.Text)
		assert.Equal(t, "text", textQuestionResult.QuestionType)
		assert.NotNil(t, textQuestionResult.StudentResponse)
		assert.False(t, textQuestionResult.StudentResponse.IsCorrect)
		assert.True(t, textQuestionResult.StudentResponse.WasAttempted)
		assert.Equal(t, "London", *textQuestionResult.StudentResponse.ResponseText)
		assert.Equal(t, "Paris", *textQuestionResult.CorrectAnswer)
	})

	// Test 2: Admin gets answer key without student responses
	t.Run("AdminAnswerKey", func(t *testing.T) {
		url := fmt.Sprintf("/api/tests/%d/answer-key", test.ID)
		resp := authenticatedRequestHelper(http.MethodGet, url, nil, adminToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var result models.TestAnswerKeyForAdmin
		err := json.Unmarshal(resp.Body.Bytes(), &result)
		assert.Nil(t, err)

		// Verify basic test info
		assert.Equal(t, test.ID, result.TestID)
		assert.Equal(t, test.Name, result.TestName)
		assert.Equal(t, testType.Name, result.TestType)
		assert.Equal(t, 2, result.MaxPossibleScore)

		// Verify sections
		assert.Equal(t, 1, len(result.Sections))
		sectionResult := result.Sections[0]
		assert.Equal(t, 2, len(sectionResult.Questions))

		// Find MCQ question
		var mcqQuestionResult *models.TestAnswerKeyQuestionAdmin
		for i := range sectionResult.Questions {
			if sectionResult.Questions[i].QuestionID == mcqQuestion.ID {
				mcqQuestionResult = &sectionResult.Questions[i]
				break
			}
		}
		assert.NotNil(t, mcqQuestionResult)
		assert.Equal(t, "What is 2+2?", mcqQuestionResult.Text)
		assert.Equal(t, "mcq", mcqQuestionResult.QuestionType)
		assert.Equal(t, 2, len(mcqQuestionResult.Options))

		// Verify correct option is marked
		var foundCorrectOption bool
		for _, option := range mcqQuestionResult.Options {
			if option.OptionText == "4" {
				assert.True(t, option.IsCorrect)
				foundCorrectOption = true
			}
		}
		assert.True(t, foundCorrectOption)

		// Find text question
		var textQuestionResult *models.TestAnswerKeyQuestionAdmin
		for i := range sectionResult.Questions {
			if sectionResult.Questions[i].QuestionID == textQuestion.ID {
				textQuestionResult = &sectionResult.Questions[i]
				break
			}
		}
		assert.NotNil(t, textQuestionResult)
		assert.Equal(t, "What is the capital of France?", textQuestionResult.Text)
		assert.Equal(t, "text", textQuestionResult.QuestionType)
		assert.Equal(t, "Paris", *textQuestionResult.CorrectAnswer)
	})

	// Test 3: Invalid test ID
	t.Run("InvalidTestID", func(t *testing.T) {
		resp := authenticatedRequestHelper(http.MethodGet, "/api/tests/abc/answer-key", nil, studentToken)
		assert.Equal(t, http.StatusBadRequest, resp.Code)
	})

	// Test 4: Non-existent test ID
	t.Run("NonExistentTestID", func(t *testing.T) {
		resp := authenticatedRequestHelper(http.MethodGet, "/api/tests/99999/answer-key", nil, studentToken)
		assert.Equal(t, http.StatusInternalServerError, resp.Code)
	})

	// Test 5: Unauthenticated request
	t.Run("UnauthenticatedRequest", func(t *testing.T) {
		url := fmt.Sprintf("/api/tests/%d/answer-key", test.ID)
		resp := requestExecutionHelper(http.MethodGet, url, nil)
		assert.Equal(t, http.StatusUnauthorized, resp.Code)
	})
}

func TestGetTestAnswerKeyForStudentWithoutResponses(t *testing.T) {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	studentEmail := "no_response_student_" + timestamp + "@example.com"
	testName := "No Response Test " + timestamp
	subjectName := "No Response Subject " + timestamp

	defer func() {
		db.Exec("DELETE FROM section_questions WHERE section_id IN (SELECT id FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?))", testName)
		db.Exec("DELETE FROM questions WHERE topic_id IN (SELECT t.id FROM topics t JOIN chapters c ON t.chapter_id = c.id JOIN subjects s ON c.subject_id = s.id WHERE s.name = ?)", subjectName)
		db.Exec("DELETE FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM tests WHERE name = ?", testName)
		db.Exec("DELETE FROM test_type_section_types WHERE test_type_id IN (SELECT id FROM test_types WHERE name LIKE ?)", "%"+timestamp+"%")
		db.Exec("DELETE FROM test_types WHERE name LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM section_types WHERE name LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM difficulties WHERE name LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
		db.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email = ?)", studentEmail)
		db.Exec("DELETE FROM users WHERE email = ?", studentEmail)
	}()

	// Create student
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "No Response Student",
			Email:          studentEmail,
			PhoneNumber:    "4444444444",
			ContactAddress: "Student Street",
		},
		ParentPhone: "5555555555",
		ParentEmail: "parent_no_resp_" + timestamp + "@example.com",
		Password:    "studentpassword123",
	}
	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var studentResponse models.CreatedStudentResponse
	err := json.Unmarshal(studentResp.Body.Bytes(), &studentResponse)
	assert.Nil(t, err)
	studentToken := studentResponse.Token

	// Create minimal test setup
	subject := models.Subject{Name: subjectName, DisplayName: "Test Subject"}
	db.Create(&subject)

	chapter := models.Chapter{Name: "Test Chapter " + timestamp, SubjectID: subject.ID}
	db.Create(&chapter)

	topic := models.Topic{Name: "Test Topic " + timestamp, ChapterID: chapter.ID}
	db.Create(&topic)

	difficulty := models.Difficulty{Name: "Easy " + timestamp}
	db.Create(&difficulty)

	question := models.Question{
		Text:         "Unanswered question?",
		TopicID:      topic.ID,
		DifficultyID: difficulty.ID,
		QuestionType: "mcq",
	}
	db.Create(&question)

	sectionType := models.SectionType{Name: "General " + timestamp, SubjectID: subject.ID}
	db.Create(&sectionType)

	testType := models.TestType{Name: "General Test " + timestamp}
	db.Create(&testType)
	db.Model(&testType).Association("SectionTypes").Append(&sectionType)

	test := models.Test{
		Name:       testName,
		TestTypeID: testType.ID,
		FromTime:   time.Now(),
		ToTime:     time.Now().Add(2 * time.Hour),
		Active:     true,
	}
	db.Create(&test)

	section := models.Section{
		Name:          "Test Section " + timestamp,
		TestID:        test.ID,
		SectionTypeID: sectionType.ID,
	}
	db.Create(&section)
	db.Model(&section).Association("Questions").Append(&question)

	// Test: Student gets answer key without having attempted the test
	url := fmt.Sprintf("/api/tests/%d/answer-key", test.ID)
	resp := authenticatedRequestHelper(http.MethodGet, url, nil, studentToken)
	assert.Equal(t, http.StatusOK, resp.Code)

	var result models.TestAnswerKeyForStudent
	err = json.Unmarshal(resp.Body.Bytes(), &result)
	assert.Nil(t, err)

	assert.Equal(t, 0, result.TotalScore)
	assert.Equal(t, 1, result.MaxPossibleScore)
	assert.Equal(t, 1, len(result.Sections))
	assert.Equal(t, 1, len(result.Sections[0].Questions))

	questionResult := result.Sections[0].Questions[0]
	assert.NotNil(t, questionResult.StudentResponse)
	assert.False(t, questionResult.StudentResponse.WasAttempted)
	assert.False(t, questionResult.StudentResponse.IsCorrect)
	assert.Equal(t, 0, *questionResult.StudentResponse.CalculatedScore)
}

func TestGetTestAnswerKeyEdgeCases(t *testing.T) {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	studentEmail := "edge_case_student_" + timestamp + "@example.com"

	defer func() {
		db.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email = ?)", studentEmail)
		db.Exec("DELETE FROM users WHERE email = ?", studentEmail)
	}()

	// Create student
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Edge Case Student",
			Email:          studentEmail,
			PhoneNumber:    "6666666666",
			ContactAddress: "Student Street",
		},
		ParentPhone: "7777777777",
		ParentEmail: "parent_edge_" + timestamp + "@example.com",
		Password:    "studentpassword123",
	}
	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var studentResponse models.CreatedStudentResponse
	err := json.Unmarshal(studentResp.Body.Bytes(), &studentResponse)
	assert.Nil(t, err)
	studentToken := studentResponse.Token

	// Test: Missing user_id in context (should not happen in real scenario)
	t.Run("MissingUserIDInContext", func(t *testing.T) {
		// This would require mocking the middleware, so we'll test with invalid token instead
		resp := authenticatedRequestHelper(http.MethodGet, "/api/tests/1/answer-key", nil, "invalid-token")
		assert.Equal(t, http.StatusUnauthorized, resp.Code)
	})

	// Test: Invalid test ID format
	t.Run("InvalidTestIDFormat", func(t *testing.T) {
		resp := authenticatedRequestHelper(http.MethodGet, "/api/tests/not-a-number/answer-key", nil, studentToken)
		assert.Equal(t, http.StatusBadRequest, resp.Code)
	})
}
