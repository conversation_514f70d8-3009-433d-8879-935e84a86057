package db

import (
    "context"
    "testing"
    "time"
    "ziaacademy-backend/internal/models"

    "github.com/stretchr/testify/assert"
    "gorm.io/driver/sqlite"
    "gorm.io/gorm"
)

func setupTestDB() *gorm.DB {
    db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    
    // Auto migrate all models
    db.AutoMigrate(
        &models.User{},
        &models.Student{},
        &models.Admin{},
        &models.Subject{},
        &models.Chapter{},
        &models.Topic{},
        &models.Difficulty{},
        &models.Question{},
        &models.Option{},
        &models.SectionType{},
        &models.TestType{},
        &models.Test{},
        &models.Section{},
        &models.TestResponse{},
    )
    
    return db
}

func TestGetTestAnswerKey_StudentRole(t *testing.T) {
    db := setupTestDB()
    plugin := &DbPlugin{db: db}
    ctx := context.Background()

    // Create test data
    user := models.User{
        FullName:       "Test Student",
        Email:          "<EMAIL>",
        PhoneNumber:    "1234567890",
        ContactAddress: "Test Address",
        Role:           "Student",
    }
    db.Create(&user)

    student := models.Student{
        UserID:      user.ID,
        ParentPhone: "0987654321",
        ParentEmail: "<EMAIL>",
        User:        user,
    }
    db.Create(&student)

    subject := models.Subject{Name: "Math", DisplayName: "Mathematics"}
    db.Create(&subject)

    chapter := models.Chapter{Name: "Algebra", SubjectID: subject.ID}
    db.Create(&chapter)

    topic := models.Topic{Name: "Linear Equations", ChapterID: chapter.ID}
    db.Create(&topic)

    difficulty := models.Difficulty{Name: "Medium"}
    db.Create(&difficulty)

    question := models.Question{
        Text:         "What is 2+2?",
        TopicID:      topic.ID,
        DifficultyID: difficulty.ID,
        QuestionType: "mcq",
    }
    db.Create(&question)

    option := models.Option{
        QuestionID: question.ID,
        OptionText: "4",
        IsCorrect:  true,
    }
    db.Create(&option)

    testType := models.TestType{Name: "Practice Test"}
    db.Create(&testType)

    test := models.Test{
        Name:       "Sample Test",
        TestTypeID: testType.ID,
        FromTime:   time.Now(),
        ToTime:     time.Now().Add(time.Hour),
        Active:     true,
    }
    db.Create(&test)

    sectionType := models.SectionType{Name: "General", SubjectID: subject.ID}
    db.Create(&sectionType)

    section := models.Section{
        Name:          "Math Section",
        TestID:        test.ID,
        SectionTypeID: sectionType.ID,
    }
    db.Create(&section)

    db.Model(&section).Association("Questions").Append(&question)

    // Create test response
    score := 1
    testResponse := models.TestResponse{
        StudentID:         student.ID,
        TestID:            test.ID,
        QuestionID:        question.ID,
        SelectedOptionIDs: []int{int(option.ID)},
        IsCorrect:         true,
        CalculatedScore:   &score,
    }
    db.Create(&testResponse)

    // Test student answer key
    result, err := plugin.GetTestAnswerKey(ctx, test.ID, user.ID, "Student")
    assert.Nil(t, err)
    assert.NotNil(t, result)

    studentResult, ok := result.(*models.TestAnswerKeyForStudent)
    assert.True(t, ok)
    assert.Equal(t, test.ID, studentResult.TestID)
    assert.Equal(t, test.Name, studentResult.TestName)
    assert.Equal(t, student.ID, studentResult.StudentID)
    assert.Equal(t, user.FullName, studentResult.StudentName)
    assert.Equal(t, 1, studentResult.TotalScore)
    assert.Equal(t, 1, studentResult.MaxPossibleScore)
    assert.Equal(t, 1, len(studentResult.Sections))
}

func TestGetTestAnswerKey_AdminRole(t *testing.T) {
    db := setupTestDB()
    plugin := &DbPlugin{db: db}
    ctx := context.Background()

    // Create test data
    subject := models.Subject{Name: "Physics", DisplayName: "Physics"}
    db.Create(&subject)

    chapter := models.Chapter{Name: "Mechanics", SubjectID: subject.ID}
    db.Create(&chapter)

    topic := models.Topic{Name: "Motion", ChapterID: chapter.ID}
    db.Create(&topic)

    difficulty := models.Difficulty{Name: "Hard"}
    db.Create(&difficulty)

    correctAnswer := "Newton"
    question := models.Question{
        Text:          "Who discovered the laws of motion?",
        TopicID:       topic.ID,
        DifficultyID:  difficulty.ID,
        QuestionType:  "text",
        CorrectAnswer: &correctAnswer,
    }
    db.Create(&question)

    testType := models.TestType{Name: "Final Exam"}
    db.Create(&testType)

    test := models.Test{
        Name:       "Physics Final",
        TestTypeID: testType.ID,
        FromTime:   time.Now(),
        ToTime:     time.Now().Add(2 * time.Hour),
        Active:     true,
    }
    db.Create(&test)

    sectionType := models.SectionType{Name: "Theory", SubjectID: subject.ID}
    db.Create(&sectionType)

    section := models.Section{
        Name:          "Physics Section",
        TestID:        test.ID,
        SectionTypeID: sectionType.ID,
    }
    db.Create(&section)

    db.Model(&section).Association("Questions").Append(&question)

    // Test admin answer key
    result, err := plugin.GetTestAnswerKey(ctx, test.ID, 1, "Admin")
    assert.Nil(t, err)
    assert.NotNil(t, result)

    adminResult, ok := result.(*models.TestAnswerKeyForAdmin)
    assert.True(t, ok)
    assert.Equal(t, test.ID, adminResult.TestID)
    assert.Equal(t, test.Name, adminResult.TestName)
    assert.Equal(t, testType.Name, adminResult.TestType)
    assert.Equal(t, 1, adminResult.MaxPossibleScore)
    assert.Equal(t, 1, len(adminResult.Sections))
    assert.Equal(t, 1, len(adminResult.Sections[0].Questions))
    assert.Equal(t, "Newton", *adminResult.Sections[0].Questions[0].CorrectAnswer)
}

func TestGetTestAnswerKey_NonExistentTest(t *testing.T) {
    db := setupTestDB()
    plugin := &DbPlugin{db: db}
    ctx := context.Background()

    result, err := plugin.GetTestAnswerKey(ctx, 99999, 1, "Student")
    assert.NotNil(t, err)
    assert.Nil(t, result)
    assert.Contains(t, err.Error(), "test not found")
}

func TestGetTestAnswerKey_StudentNotFound(t *testing.T) {
    db := setupTestDB()
    plugin := &DbPlugin{db: db}
    ctx := context.Background()

    // Create test without student
    testType := models.TestType{Name: "Test Type"}
    db.Create(&testType)

    test := models.Test{
        Name:       "Test",
        TestTypeID: testType.ID,
        FromTime:   time.Now(),
        ToTime:     time.Now().Add(time.Hour),
        Active:     true,
    }
    db.Create(&test)

    result, err := plugin.GetTestAnswerKey(ctx, test.ID, 99999, "Student")
    assert.NotNil(t, err)
    assert.Nil(t, result)
    assert.Contains(t, err.Error(), "failed to get student info")
}